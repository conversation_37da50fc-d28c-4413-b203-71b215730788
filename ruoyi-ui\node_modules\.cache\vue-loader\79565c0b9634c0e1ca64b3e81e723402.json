{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summaryConfirm.vue?vue&type=style&index=0&id=3292afc6&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summaryConfirm.vue", "mtime": 1753958088613}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmluZm8gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCi5jb250ZW50IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgd2lkdGg6IDcwJTsNCiAgbWluLWhlaWdodDogNjR2aDsNCiAgbWFyZ2luOiAyMHB4IDA7DQp9DQouaGVhZGVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE3NmFkYjsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIC5jZW50ZXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZm9udC1zaXplOiAyMnB4Ow0KICAgIGNvbG9yOiAjZmZmZmZmOw0KICB9DQp9DQouZWwtbWFpbiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGNvbG9yOiAjMzMzOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGxpbmUtaGVpZ2h0OiA2MHB4Ow0KICBwYWRkaW5nOiA2MHB4IDExMHB4Ow0KfQ0KLml0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBtYXJnaW4tYm90dG9tOiA4MHB4Ow0KICAuaXRlbS10aXRsZSB7DQogICAgd2lkdGg6IDEyMHB4Ow0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICB9DQp9DQoubGl0dGxlLXRpdGxlIHsNCiAgY29sb3I6IHJnYmEoODAsIDgwLCA4MCwgMSk7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCi5pdGVtLWJ1dHRvbiB7DQogIHdpZHRoOiAxNTVweDsNCiAgaGVpZ2h0OiA0OHB4Ow0KICBtYXJnaW46IDIwcHggMjhweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE3NmFkYjsNCiAgY29sb3I6ICNmZmY7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQp9DQouaXRlbS1idXR0b24tbGl0dGxlIHsNCiAgYm9yZGVyOiAjMzMzIDFweCBzb2xpZDsNCiAgd2lkdGg6IDEyNHB4Ow0KICBoZWlnaHQ6IDMycHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTUxLCAyNTMsIDI0NiwgMSk7DQogIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDEpOw0KICAmOmhvdmVyIHsNCiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAxKTsNCiAgfQ0KfQ0KLmZhY3RvcnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQo="}, {"version": 3, "sources": ["summaryConfirm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "summaryConfirm.vue", "sourceRoot": "src/views/expertReview", "sourcesContent": ["<template>\r\n  <div>\r\n    <BidHeadthree></BidHeadthree>\r\n    <div class=\"info\">\r\n      <div class=\"content\">\r\n        <el-header\r\n          height=\"70px\"\r\n          class=\"header\"\r\n        >\r\n          <div class=\"center\">\r\n            专家复核\r\n          </div>\r\n        </el-header>\r\n        <el-main>\r\n          <confirmTable\r\n            :label=\"'资格性评审'\"\r\n            :tableData=\"qualificationTableData\"\r\n            :column=\"column\"\r\n            @update=\"init\"\r\n          ></confirmTable>\r\n          <el-divider></el-divider>\r\n\r\n          <confirmTable\r\n            :label=\"'符合性评审'\"\r\n            :tableData=\"complianceTableData\"\r\n            :column=\"column\"\r\n            @update=\"init\"\r\n          ></confirmTable>\r\n          <el-divider></el-divider>\r\n\r\n          <template v-if=\"project.tenderMode!=3\">\r\n            <confirmTable\r\n                :label=\"'技术标评审'\"\r\n                :tableData=\"technicalTableData\"\r\n                :column=\"column\"\r\n                @update=\"init\"\r\n            ></confirmTable>\r\n            <el-divider></el-divider>\r\n\r\n            <confirmTable\r\n                :label=\"'商务标评审'\"\r\n                :tableData=\"businessTableData\"\r\n                :column=\"column\"\r\n                @update=\"init\"\r\n            ></confirmTable>\r\n          </template>\r\n          <div class=\"operation\">\r\n            <el-button\r\n              class=\"item-button\"\r\n              @click=\"completed\"\r\n            >返回</el-button>\r\n          </div>\r\n        </el-main>\r\n      </div>\r\n    </div>\r\n    <Foot></Foot>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { getProject } from \"@/api/tender/project\";\r\nimport confirmTable from \"./summaryConfirm/table\";\r\nimport { confirmList } from \"@/api/evaluation/detail\";\r\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\r\n\r\nexport default {\r\n  name: \"summaryConfirm\",\r\n  components: { confirmTable },\r\n  mixins: [expertReviewWebSocket],\r\n  data() {\r\n    return {\r\n      project: {\r\n        projectName: \"\",\r\n      },\r\n      qualificationTableData: [],\r\n      complianceTableData: [],\r\n      technicalTableData: [],\r\n      businessTableData: [],\r\n      tenderOffer: [],\r\n      column: [],\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      // 根据项目id查询项目信息\r\n      getProject(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.project = response.data;\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // const expertInfo = JSON.parse(localStorage.getItem(\"expertResultId\"));\r\n      const data = {\r\n        expertResultId: localStorage.getItem(\"expertResultId\"),\r\n        projectId: this.$route.query.projectId,\r\n      };\r\n      // 初始化列表\r\n      confirmList(data).then((response) => {\r\n        if (response.code == 200) {\r\n          const busiBidderInfos = response.data.busiBidderInfos;\r\n          const uitems = response.data.uitems;\r\n          const scoringMethodItems = response.data.scoringMethodItems;\r\n\r\n          this.column = busiBidderInfos.map((bidder) => {\r\n            return bidder.bidderName;\r\n          });\r\n\r\n          scoringMethodItems.map((name) => {\r\n            // 生成资格性评审格式\r\n            const qualificationReview = uitems[name.scoringMethodItemId].map(\r\n              (item) => {\r\n                const result = {\r\n                  factor: item.itemName,\r\n                };\r\n                if (item.evalExpertEvaluationDetails) {\r\n                  item.evalExpertEvaluationDetails.forEach((detail) => {\r\n                    // 根据entId找到供应商名称\r\n                    const bidder = busiBidderInfos.find(\r\n                      (bidder) => bidder.bidderId === detail.entId\r\n                    );\r\n                    if (bidder) {\r\n                      result[bidder.bidderName] = {\r\n                        evaluationResult: detail.evaluationResult,\r\n                        expertEvaluationId: detail.expertEvaluationId,\r\n                        scoreLevel: item.scoreLevel,\r\n                      };\r\n                    }\r\n                  });\r\n                }\r\n                console.log(result);\r\n                return result;\r\n              }\r\n            );\r\n\r\n            switch (name.itemName) {\r\n              case \"资格性评审\":\r\n                this.qualificationTableData = qualificationReview;\r\n                break;\r\n              case \"技术标评审\":\r\n                this.technicalTableData = qualificationReview;\r\n                break;\r\n              case \"商务标评审\":\r\n                this.businessTableData = qualificationReview;\r\n                break;\r\n              case \"符合性评审\":\r\n                this.complianceTableData = qualificationReview;\r\n                break;\r\n              case \"投标报价打分\":\r\n                this.tenderOffer = qualificationReview;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 确认\r\n    completed() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,\r\n          zjhm: this.$route.query.zjhm,\r\n        },\r\n      });\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info {\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.content {\r\n  background-color: #fff;\r\n  width: 70%;\r\n  min-height: 64vh;\r\n  margin: 20px 0;\r\n}\r\n.header {\r\n  background-color: #176adb;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  .center {\r\n    display: flex;\r\n    font-size: 22px;\r\n    color: #ffffff;\r\n  }\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n  padding: 60px 110px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.little-title {\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 14px;\r\n}\r\n.item-button {\r\n  width: 155px;\r\n  height: 48px;\r\n  margin: 20px 28px;\r\n  background-color: #176adb;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.item-button-little {\r\n  border: #333 1px solid;\r\n  width: 124px;\r\n  height: 32px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n</style>\r\n"]}]}