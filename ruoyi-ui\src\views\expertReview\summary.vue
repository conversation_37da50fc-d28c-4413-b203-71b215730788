<template>
  <div>
    <BidHeadthree></BidHeadthree>
    <div class="info">
      <div class="content">
        <div style="position: relative;right: 60px;margin-bottom:20px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0">评审汇总</div>
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="headStyle"
          :cell-style="cellStyle"
        >
          <el-table-column
            prop="供应商名称"
            label="供应商名称"
          ></el-table-column>
          <el-table-column
            v-for="(item, index) in scoringItems"
            :key="index"
            :prop="item.itemName"
            :label="item.itemName"
          ></el-table-column>
        </el-table>
        <div class="operation">
          <el-button
            class="item-button"
            @click="evaluationEnd"
            v-if="expertInfo.expertLeader==1"
          >评审结束</el-button>
          <el-button
            style="background: #F5F5F5;color:#176ADB"
            class="item-button"
            @click="printReport"
          >下载评审报告</el-button>
          <el-button
            class="item-button"
            @click="printResult"
            v-if="expertInfo.expertLeader==1"
          >下载评审结果</el-button>
          <el-button
            style="background: #F5F5F5;color:#176ADB"
            class="item-button"
            @click="back"
          >返回</el-button>
        </div>
      </div>
    </div>
    <Foot></Foot>
  </div>

</template>

<script>
import { getProject } from "@/api/tender/project";
import { reviewSummary } from "@/api/expert/review";
import { formatDate } from "@/utils/date";
import {
  updateInfo,
  listInfo,
  exportReport,
  exportResult,
  exportEvalProjectEvaluationInfo,
} from "@/api/evaluation/info";
import axios from "axios";
import { expertInfoById } from "../../api/expert/review";
import expertReviewWebSocket from "@/mixins/expertReviewWebSocket";

export default {
  name: "qualification",
  mixins: [expertReviewWebSocket],
  data() {
    return {
      project: {},
      expertInfo: {},
      bidderInfos: [],
      projectName: "测试项目111",
      tableData: [],
      data: {},
      scoringItems: [],
      headStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
      // 定时器ID，用于清除定时器
      intervalId: null,
    };
  },
  methods: {
    init() {
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
      this.expertInfo = expertInfo;
      // 根据项目id查询项目信息
      getProject(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.project = response.data;
        } else {
          this.$message.warning(response.msg);
        }
      });
      reviewSummary({
        projectId: this.$route.query.projectId,
        resultId: expertInfo.resultId,
      }).then((response) => {
        if (response.code == 200) {
          this.data = response.data;
          this.scoringItems = this.data.scoringMethodItems;
          this.tableData = this.transformData(this.data);
          this.initBidderInfos(this.data);
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    initBidderInfos(data) {
      let bidderScores = [];
      for (let index in data.busiBidderInfos) {
        let bidder = data.busiBidderInfos[index];
        let bidderInfo = {};
        bidderInfo.bidderInfoId = bidder.bidderInfoId;
        bidderInfo.bidderId = bidder.bidderId;
        let scores = data.resultMap[bidder.bidderId];
        let bidderScore = 0;
        if (scores[1] != undefined && scores[1] != null) {
          bidderScore = parseFloat(scores[1]);
        }
        bidderInfo.score = bidderScore;
        this.bidderInfos.push(bidderInfo);
      }

      if(this.project.tenderMode==3){
        this.bidderInfos.sort((a, b) => b.score - a.score);
      }else{
        this.bidderInfos.sort((a, b) => a.score - b.score);
      }
      this.bidderInfos.forEach(function (value, index) {
        value.ranking = index + 1;
      });
    },
    evaluationEnd() {
			// 此处增加一个确认弹框
	    this.$confirm("确定结束本次评审吗？", "提示", {
				confirmButtonText: "确定",
		    cancelButtonText: "取消",
		    type: "warning"}
	    ).then(()=>{
		    listInfo({
			    projectId: this.$route.query.projectId,
		    }).then((response) => {
			    if (response.code == 200) {
				    updateInfo({
					    projectEvaluationId: response.rows[0].projectEvaluationId,
					    projectId: this.$route.query.projectId,
					    evaluationEndTime: formatDate(new Date()),
					    bidderInfos: this.bidderInfos,
				    }).then((response) => {
					    if (response.code == 200) {
						    this.$message.success(response.msg);
					    } else {
						    this.$message.warning(response.msg);
					    }
				    });
			    } else {
				    this.$message.warning(response.msg);
			    }
		    });
	    })
     
    },
    // 生成转换函数
    transformData(data) {
      // 提取评分项的名称
      const scoringItems = data.scoringMethodItems.reduce((acc, item) => {
        acc[item.scoringMethodItemId] = item.itemName;
        return acc;
      }, {});

      // 组装并过滤最终数据
      const result = data.busiBidderInfos
        .map((bidder) => {
          const bidderId = bidder.bidderId;
          const scores = data.resultMap[bidderId] || {};

          const rowData = {
            供应商名称: bidder.bidderName,
            总分: scores["1"] || "-",
          };

          // 为每个评分项添加数据
          for (const [id, name] of Object.entries(scoringItems)) {
            rowData[name] = scores[id] || "-";
          }

          return rowData;
        })
        .filter((item) => item.供应商名称 !== "总分"); // 使用 filter 来排除供应商名称为“总分”的记录

      return result;
    },
    printReport() {
      var projectId = Number(this.$route.query.projectId);
      var resultId = this.expertInfo.resultId;

			// 获取专家组长的resultId
	    expertInfoById({
		    projectId
	    }).then((response) => {
		    console.log(response)

		    // 筛选出专家组长
		    let selectExpertTeamLeaders = response.data.filter(item => item.expertLeader == 1);


		    var url =
			    process.env.VUE_APP_BASE_API +
			    "/evaluation/info/saveReviewReport?projectId=" +
			    projectId +
			    "&resultId=" +
			    selectExpertTeamLeaders[0].resultId;

		    var newWindow = window.open(url, "_blank");
		    setTimeout(() => {
			    if (newWindow) {
				    newWindow.document.title = "磋商文件";
			    }
		    }, 500); // 延迟执行以确保新窗口已经加载
			});


    },
    printResult() {
      var projectId = Number(this.$route.query.projectId);
      var resultId = this.expertInfo.resultId;
      var url =
        process.env.VUE_APP_BASE_API +
        "/evaluation/info/exportEvalProjectEvaluationInfo?projectId=" +
        projectId +
        "&resultId=" +
        resultId;
      window.location.href = url; // var url = "http://localhost:8080
    },
    back() {
      this.$router.push({
        path: "/expertHome",
        query: { zjhm: this.$route.query.zjhm },
      });
    },

    /**
     * 清除定时器的通用方法
     * 在多个生命周期钩子中调用，确保定时器被正确清除
     */
    clearTimer() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
        console.log("定时器已清除 - compliance/three.vue");
      }
    },
  },
  mounted() {
    this.init();
    // 启用定时器，每5秒刷新一次数据以保持实时更新
    this.intervalId = setInterval(()=>{
      this.init();
    },5000)
  },

  /**
   * 组件销毁前执行
   * 清除定时器，防止内存泄漏
   */
  beforeDestroy() {
    this.clearTimer();
  },

  /**
   * 组件完全销毁后执行
   * 作为额外的安全措施清除定时器
   */
  destroyed() {
    this.clearTimer();
  },

  /**
   * 如果父组件使用了keep-alive，在组件失活时清除定时器
   */
  deactivated() {
    this.clearTimer();
  },
};
</script>

<style lang="scss" scoped>
.info {
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content {
  background-color: #fff;
  width: 70%;
  min-height: 64vh;
  margin: 20px 0;
  padding: 20px 110px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.little-title {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
}
.item-button {
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: #176adb;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.item-button-little {
  border: #333 1px solid;
  width: 124px;
  height: 32px;
  background-color: rgba(151, 253, 246, 1);
  color: rgba(0, 0, 0, 1);
  &:hover {
    color: rgba(0, 0, 0, 1);
  }
}
.factors {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

