{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summary.vue?vue&type=style&index=0&id=19c8995a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summary.vue", "mtime": 1753958075252}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5pbmZvIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQouY29udGVudCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIHdpZHRoOiA3MCU7DQogIG1pbi1oZWlnaHQ6IDY0dmg7DQogIG1hcmdpbjogMjBweCAwOw0KICBwYWRkaW5nOiAyMHB4IDExMHB4Ow0KfQ0KLml0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBtYXJnaW4tYm90dG9tOiA4MHB4Ow0KICAuaXRlbS10aXRsZSB7DQogICAgd2lkdGg6IDEyMHB4Ow0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICB9DQp9DQoubGl0dGxlLXRpdGxlIHsNCiAgY29sb3I6IHJnYmEoODAsIDgwLCA4MCwgMSk7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCi5pdGVtLWJ1dHRvbiB7DQogIHdpZHRoOiAxNTVweDsNCiAgaGVpZ2h0OiA0OHB4Ow0KICBtYXJnaW46IDIwcHggMjhweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE3NmFkYjsNCiAgY29sb3I6ICNmZmY7DQogICY6aG92ZXIgew0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQp9DQouaXRlbS1idXR0b24tbGl0dGxlIHsNCiAgYm9yZGVyOiAjMzMzIDFweCBzb2xpZDsNCiAgd2lkdGg6IDEyNHB4Ow0KICBoZWlnaHQ6IDMycHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTUxLCAyNTMsIDI0NiwgMSk7DQogIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDEpOw0KICAmOmhvdmVyIHsNCiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAxKTsNCiAgfQ0KfQ0KLmZhY3RvcnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoub3BlcmF0aW9uIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQo="}, {"version": 3, "sources": ["summary.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "summary.vue", "sourceRoot": "src/views/expertReview", "sourcesContent": ["<template>\r\n  <div>\r\n    <BidHeadthree></BidHeadthree>\r\n    <div class=\"info\">\r\n      <div class=\"content\">\r\n        <div style=\"position: relative;right: 60px;margin-bottom:20px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0\">评审汇总</div>\r\n        <el-table\r\n          :data=\"tableData\"\r\n          style=\"width: 100%\"\r\n          :header-cell-style=\"headStyle\"\r\n          :cell-style=\"cellStyle\"\r\n        >\r\n          <el-table-column\r\n            prop=\"供应商名称\"\r\n            label=\"供应商名称\"\r\n          ></el-table-column>\r\n          <el-table-column\r\n            v-for=\"(item, index) in scoringItems\"\r\n            :key=\"index\"\r\n            :prop=\"item.itemName\"\r\n            :label=\"item.itemName\"\r\n          ></el-table-column>\r\n        </el-table>\r\n        <div class=\"operation\">\r\n          <el-button\r\n            class=\"item-button\"\r\n            @click=\"evaluationEnd\"\r\n            v-if=\"expertInfo.expertLeader==1\"\r\n          >评审结束</el-button>\r\n          <el-button\r\n            style=\"background: #F5F5F5;color:#176ADB\"\r\n            class=\"item-button\"\r\n            @click=\"printReport\"\r\n          >下载评审报告</el-button>\r\n          <el-button\r\n            class=\"item-button\"\r\n            @click=\"printResult\"\r\n            v-if=\"expertInfo.expertLeader==1\"\r\n          >下载评审结果</el-button>\r\n          <el-button\r\n            style=\"background: #F5F5F5;color:#176ADB\"\r\n            class=\"item-button\"\r\n            @click=\"back\"\r\n          >返回</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <Foot></Foot>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { getProject } from \"@/api/tender/project\";\r\nimport { reviewSummary } from \"@/api/expert/review\";\r\nimport { formatDate } from \"@/utils/date\";\r\nimport {\r\n  updateInfo,\r\n  listInfo,\r\n  exportReport,\r\n  exportResult,\r\n  exportEvalProjectEvaluationInfo,\r\n} from \"@/api/evaluation/info\";\r\nimport axios from \"axios\";\r\nimport { expertInfoById } from \"../../api/expert/review\";\r\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\r\n\r\nexport default {\r\n  name: \"qualification\",\r\n  mixins: [expertReviewWebSocket],\r\n  data() {\r\n    return {\r\n      project: {},\r\n      expertInfo: {},\r\n      bidderInfos: [],\r\n      projectName: \"测试项目111\",\r\n      tableData: [],\r\n      data: {},\r\n      scoringItems: [],\r\n      headStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        background: \"#176ADB\",\r\n        color: \"#fff\",\r\n        \"font-size\": \"16px\",\r\n        \"font-weight\": \"700\",\r\n        border: \"0\",\r\n      },\r\n      cellStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        height: \"60px\",\r\n        color: \"#000\",\r\n        \"font-size\": \"14px\",\r\n        \"font-weight\": \"700\",\r\n      },\r\n      // 定时器ID，用于清除定时器\r\n      intervalId: null,\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const expertInfo = JSON.parse(localStorage.getItem(\"expertInfo\"));\r\n      this.expertInfo = expertInfo;\r\n      // 根据项目id查询项目信息\r\n      getProject(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.project = response.data;\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      reviewSummary({\r\n        projectId: this.$route.query.projectId,\r\n        resultId: expertInfo.resultId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.data = response.data;\r\n          this.scoringItems = this.data.scoringMethodItems;\r\n          this.tableData = this.transformData(this.data);\r\n          this.initBidderInfos(this.data);\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    initBidderInfos(data) {\r\n      let bidderScores = [];\r\n      for (let index in data.busiBidderInfos) {\r\n        let bidder = data.busiBidderInfos[index];\r\n        let bidderInfo = {};\r\n        bidderInfo.bidderInfoId = bidder.bidderInfoId;\r\n        bidderInfo.bidderId = bidder.bidderId;\r\n        let scores = data.resultMap[bidder.bidderId];\r\n        let bidderScore = 0;\r\n        if (scores[1] != undefined && scores[1] != null) {\r\n          bidderScore = parseFloat(scores[1]);\r\n        }\r\n        bidderInfo.score = bidderScore;\r\n        this.bidderInfos.push(bidderInfo);\r\n      }\r\n\r\n      if(this.project.tenderMode==3){\r\n        this.bidderInfos.sort((a, b) => b.score - a.score);\r\n      }else{\r\n        this.bidderInfos.sort((a, b) => a.score - b.score);\r\n      }\r\n      this.bidderInfos.forEach(function (value, index) {\r\n        value.ranking = index + 1;\r\n      });\r\n    },\r\n    evaluationEnd() {\r\n\t\t\t// 此处增加一个确认弹框\r\n\t    this.$confirm(\"确定结束本次评审吗？\", \"提示\", {\r\n\t\t\t\tconfirmButtonText: \"确定\",\r\n\t\t    cancelButtonText: \"取消\",\r\n\t\t    type: \"warning\"}\r\n\t    ).then(()=>{\r\n\t\t    listInfo({\r\n\t\t\t    projectId: this.$route.query.projectId,\r\n\t\t    }).then((response) => {\r\n\t\t\t    if (response.code == 200) {\r\n\t\t\t\t    updateInfo({\r\n\t\t\t\t\t    projectEvaluationId: response.rows[0].projectEvaluationId,\r\n\t\t\t\t\t    projectId: this.$route.query.projectId,\r\n\t\t\t\t\t    evaluationEndTime: formatDate(new Date()),\r\n\t\t\t\t\t    bidderInfos: this.bidderInfos,\r\n\t\t\t\t    }).then((response) => {\r\n\t\t\t\t\t    if (response.code == 200) {\r\n\t\t\t\t\t\t    this.$message.success(response.msg);\r\n\t\t\t\t\t    } else {\r\n\t\t\t\t\t\t    this.$message.warning(response.msg);\r\n\t\t\t\t\t    }\r\n\t\t\t\t    });\r\n\t\t\t    } else {\r\n\t\t\t\t    this.$message.warning(response.msg);\r\n\t\t\t    }\r\n\t\t    });\r\n\t    })\r\n     \r\n    },\r\n    // 生成转换函数\r\n    transformData(data) {\r\n      // 提取评分项的名称\r\n      const scoringItems = data.scoringMethodItems.reduce((acc, item) => {\r\n        acc[item.scoringMethodItemId] = item.itemName;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 组装并过滤最终数据\r\n      const result = data.busiBidderInfos\r\n        .map((bidder) => {\r\n          const bidderId = bidder.bidderId;\r\n          const scores = data.resultMap[bidderId] || {};\r\n\r\n          const rowData = {\r\n            供应商名称: bidder.bidderName,\r\n            总分: scores[\"1\"] || \"-\",\r\n          };\r\n\r\n          // 为每个评分项添加数据\r\n          for (const [id, name] of Object.entries(scoringItems)) {\r\n            rowData[name] = scores[id] || \"-\";\r\n          }\r\n\r\n          return rowData;\r\n        })\r\n        .filter((item) => item.供应商名称 !== \"总分\"); // 使用 filter 来排除供应商名称为“总分”的记录\r\n\r\n      return result;\r\n    },\r\n    printReport() {\r\n      var projectId = Number(this.$route.query.projectId);\r\n      var resultId = this.expertInfo.resultId;\r\n\r\n\t\t\t// 获取专家组长的resultId\r\n\t    expertInfoById({\r\n\t\t    projectId\r\n\t    }).then((response) => {\r\n\t\t    console.log(response)\r\n\r\n\t\t    // 筛选出专家组长\r\n\t\t    let selectExpertTeamLeaders = response.data.filter(item => item.expertLeader == 1);\r\n\r\n\r\n\t\t    var url =\r\n\t\t\t    process.env.VUE_APP_BASE_API +\r\n\t\t\t    \"/evaluation/info/saveReviewReport?projectId=\" +\r\n\t\t\t    projectId +\r\n\t\t\t    \"&resultId=\" +\r\n\t\t\t    selectExpertTeamLeaders[0].resultId;\r\n\r\n\t\t    var newWindow = window.open(url, \"_blank\");\r\n\t\t    setTimeout(() => {\r\n\t\t\t    if (newWindow) {\r\n\t\t\t\t    newWindow.document.title = \"磋商文件\";\r\n\t\t\t    }\r\n\t\t    }, 500); // 延迟执行以确保新窗口已经加载\r\n\t\t\t});\r\n\r\n\r\n    },\r\n    printResult() {\r\n      var projectId = Number(this.$route.query.projectId);\r\n      var resultId = this.expertInfo.resultId;\r\n      var url =\r\n        process.env.VUE_APP_BASE_API +\r\n        \"/evaluation/info/exportEvalProjectEvaluationInfo?projectId=\" +\r\n        projectId +\r\n        \"&resultId=\" +\r\n        resultId;\r\n      window.location.href = url; // var url = \"http://localhost:8080\r\n    },\r\n    back() {\r\n      this.$router.push({\r\n        path: \"/expertHome\",\r\n        query: { zjhm: this.$route.query.zjhm },\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 清除定时器的通用方法\r\n     * 在多个生命周期钩子中调用，确保定时器被正确清除\r\n     */\r\n    clearTimer() {\r\n      if (this.intervalId) {\r\n        clearInterval(this.intervalId);\r\n        this.intervalId = null;\r\n        console.log(\"定时器已清除 - compliance/three.vue\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    // 启用定时器，每5秒刷新一次数据以保持实时更新\r\n    this.intervalId = setInterval(()=>{\r\n      this.init();\r\n    },5000)\r\n  },\r\n\r\n  /**\r\n   * 组件销毁前执行\r\n   * 清除定时器，防止内存泄漏\r\n   */\r\n  beforeDestroy() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 组件完全销毁后执行\r\n   * 作为额外的安全措施清除定时器\r\n   */\r\n  destroyed() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 如果父组件使用了keep-alive，在组件失活时清除定时器\r\n   */\r\n  deactivated() {\r\n    this.clearTimer();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info {\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.content {\r\n  background-color: #fff;\r\n  width: 70%;\r\n  min-height: 64vh;\r\n  margin: 20px 0;\r\n  padding: 20px 110px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.little-title {\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 14px;\r\n}\r\n.item-button {\r\n  width: 155px;\r\n  height: 48px;\r\n  margin: 20px 28px;\r\n  background-color: #176adb;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.item-button-little {\r\n  border: #333 1px solid;\r\n  width: 124px;\r\n  height: 32px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.operation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n</style>\r\n\r\n"]}]}