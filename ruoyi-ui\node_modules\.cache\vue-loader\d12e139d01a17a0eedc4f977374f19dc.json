{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summaryConfirm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summaryConfirm.vue", "mtime": 1753958088613}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["summaryConfirm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "summaryConfirm.vue", "sourceRoot": "src/views/expertReview", "sourcesContent": ["<template>\r\n  <div>\r\n    <BidHeadthree></BidHeadthree>\r\n    <div class=\"info\">\r\n      <div class=\"content\">\r\n        <el-header\r\n          height=\"70px\"\r\n          class=\"header\"\r\n        >\r\n          <div class=\"center\">\r\n            专家复核\r\n          </div>\r\n        </el-header>\r\n        <el-main>\r\n          <confirmTable\r\n            :label=\"'资格性评审'\"\r\n            :tableData=\"qualificationTableData\"\r\n            :column=\"column\"\r\n            @update=\"init\"\r\n          ></confirmTable>\r\n          <el-divider></el-divider>\r\n\r\n          <confirmTable\r\n            :label=\"'符合性评审'\"\r\n            :tableData=\"complianceTableData\"\r\n            :column=\"column\"\r\n            @update=\"init\"\r\n          ></confirmTable>\r\n          <el-divider></el-divider>\r\n\r\n          <template v-if=\"project.tenderMode!=3\">\r\n            <confirmTable\r\n                :label=\"'技术标评审'\"\r\n                :tableData=\"technicalTableData\"\r\n                :column=\"column\"\r\n                @update=\"init\"\r\n            ></confirmTable>\r\n            <el-divider></el-divider>\r\n\r\n            <confirmTable\r\n                :label=\"'商务标评审'\"\r\n                :tableData=\"businessTableData\"\r\n                :column=\"column\"\r\n                @update=\"init\"\r\n            ></confirmTable>\r\n          </template>\r\n          <div class=\"operation\">\r\n            <el-button\r\n              class=\"item-button\"\r\n              @click=\"completed\"\r\n            >返回</el-button>\r\n          </div>\r\n        </el-main>\r\n      </div>\r\n    </div>\r\n    <Foot></Foot>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { getProject } from \"@/api/tender/project\";\r\nimport confirmTable from \"./summaryConfirm/table\";\r\nimport { confirmList } from \"@/api/evaluation/detail\";\r\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\r\n\r\nexport default {\r\n  name: \"summaryConfirm\",\r\n  components: { confirmTable },\r\n  mixins: [expertReviewWebSocket],\r\n  data() {\r\n    return {\r\n      project: {\r\n        projectName: \"\",\r\n      },\r\n      qualificationTableData: [],\r\n      complianceTableData: [],\r\n      technicalTableData: [],\r\n      businessTableData: [],\r\n      tenderOffer: [],\r\n      column: [],\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      // 根据项目id查询项目信息\r\n      getProject(this.$route.query.projectId).then((response) => {\r\n        if (response.code == 200) {\r\n          this.project = response.data;\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n      // const expertInfo = JSON.parse(localStorage.getItem(\"expertResultId\"));\r\n      const data = {\r\n        expertResultId: localStorage.getItem(\"expertResultId\"),\r\n        projectId: this.$route.query.projectId,\r\n      };\r\n      // 初始化列表\r\n      confirmList(data).then((response) => {\r\n        if (response.code == 200) {\r\n          const busiBidderInfos = response.data.busiBidderInfos;\r\n          const uitems = response.data.uitems;\r\n          const scoringMethodItems = response.data.scoringMethodItems;\r\n\r\n          this.column = busiBidderInfos.map((bidder) => {\r\n            return bidder.bidderName;\r\n          });\r\n\r\n          scoringMethodItems.map((name) => {\r\n            // 生成资格性评审格式\r\n            const qualificationReview = uitems[name.scoringMethodItemId].map(\r\n              (item) => {\r\n                const result = {\r\n                  factor: item.itemName,\r\n                };\r\n                if (item.evalExpertEvaluationDetails) {\r\n                  item.evalExpertEvaluationDetails.forEach((detail) => {\r\n                    // 根据entId找到供应商名称\r\n                    const bidder = busiBidderInfos.find(\r\n                      (bidder) => bidder.bidderId === detail.entId\r\n                    );\r\n                    if (bidder) {\r\n                      result[bidder.bidderName] = {\r\n                        evaluationResult: detail.evaluationResult,\r\n                        expertEvaluationId: detail.expertEvaluationId,\r\n                        scoreLevel: item.scoreLevel,\r\n                      };\r\n                    }\r\n                  });\r\n                }\r\n                console.log(result);\r\n                return result;\r\n              }\r\n            );\r\n\r\n            switch (name.itemName) {\r\n              case \"资格性评审\":\r\n                this.qualificationTableData = qualificationReview;\r\n                break;\r\n              case \"技术标评审\":\r\n                this.technicalTableData = qualificationReview;\r\n                break;\r\n              case \"商务标评审\":\r\n                this.businessTableData = qualificationReview;\r\n                break;\r\n              case \"符合性评审\":\r\n                this.complianceTableData = qualificationReview;\r\n                break;\r\n              case \"投标报价打分\":\r\n                this.tenderOffer = qualificationReview;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 确认\r\n    completed() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,\r\n          zjhm: this.$route.query.zjhm,\r\n        },\r\n      });\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info {\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.content {\r\n  background-color: #fff;\r\n  width: 70%;\r\n  min-height: 64vh;\r\n  margin: 20px 0;\r\n}\r\n.header {\r\n  background-color: #176adb;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  .center {\r\n    display: flex;\r\n    font-size: 22px;\r\n    color: #ffffff;\r\n  }\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n  padding: 60px 110px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.little-title {\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 14px;\r\n}\r\n.item-button {\r\n  width: 155px;\r\n  height: 48px;\r\n  margin: 20px 28px;\r\n  background-color: #176adb;\r\n  color: #fff;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.item-button-little {\r\n  border: #333 1px solid;\r\n  width: 124px;\r\n  height: 32px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n</style>\r\n"]}]}