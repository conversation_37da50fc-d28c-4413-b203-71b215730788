/**
 * 专家评审WebSocket混入
 * 用于处理重新评审通知和页面状态同步
 */
export default {
  data() {
    return {
      // WebSocket连接实例
      reviewWebSocket: null,
      // WebSocket连接URL
      reviewWebSocketUrl: '',
      // 心跳检测定时器
      heartbeatTimer: null,
      // 重连定时器
      reconnectTimer: null,
      // 重连次数
      reconnectCount: 0,
      // 最大重连次数
      maxReconnectCount: 5,
      // 本地存储监听器
      storageListener: null,
    };
  },

  methods: {
    /**
     * 初始化WebSocket连接
     */
    initReviewWebSocket() {
      try {
        // 获取专家信息
        const expertInfo = JSON.parse(localStorage.getItem("expertInfo") || "{}");
        const projectId = this.$route.query.projectId;

        if (!expertInfo.resultId || !projectId) {
          console.warn("缺少专家信息或项目ID，无法建立WebSocket连接");
          return;
        }

        // 构建WebSocket URL
        this.reviewWebSocketUrl = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${expertInfo.resultId}/${projectId}/1`;

        this.connectReviewWebSocket();
        this.initStorageListener();
      } catch (error) {
        console.error("初始化WebSocket连接失败:", error);
      }
    },

    /**
     * 初始化本地存储监听器
     */
    initStorageListener() {
      this.storageListener = (event) => {
        const projectId = this.$route.query.projectId;

        // 处理重新评审通知
        if (event.key === 'reEvaluationNotification') {
          const data = JSON.parse(event.newValue || '{}');

          // 检查是否是当前项目的重新评审通知
          if (data.projectId === projectId) {
            this.handleReEvaluationMessage(data);
          }
        }

        // 处理专家信息更新通知
        if (event.key === 'expertInfoUpdateNotification') {
          const data = JSON.parse(event.newValue || '{}');

          // 检查是否是当前项目的专家信息更新通知
          if (data.projectId === projectId) {
            this.handleExpertInfoUpdateMessage(data);
          }
        }

        // 处理分值修改通知
        if (event.key === 'scoreUpdateNotification') {
          const data = JSON.parse(event.newValue || '{}');

          // 检查是否是当前项目的分值修改通知
          if (data.projectId === projectId) {
            this.handleScoreUpdateMessage(data);
          }
        }
      };

      window.addEventListener('storage', this.storageListener);
    },

    /**
     * 建立WebSocket连接
     */
    connectReviewWebSocket() {
      if (this.reviewWebSocket && this.reviewWebSocket.readyState === WebSocket.OPEN) {
        return;
      }

      try {
        this.reviewWebSocket = new WebSocket(this.reviewWebSocketUrl);
        
        this.reviewWebSocket.onopen = this.onWebSocketOpen;
        this.reviewWebSocket.onmessage = this.onWebSocketMessage;
        this.reviewWebSocket.onclose = this.onWebSocketClose;
        this.reviewWebSocket.onerror = this.onWebSocketError;
      } catch (error) {
        console.error("WebSocket连接失败:", error);
        this.scheduleReconnect();
      }
    },

    /**
     * WebSocket连接打开事件
     */
    onWebSocketOpen() {
      console.log("专家评审WebSocket连接已建立");
      this.reconnectCount = 0;
      this.startHeartbeat();
    },

    /**
     * WebSocket消息接收事件
     */
    onWebSocketMessage(event) {
      try {
        // 处理心跳消息
        if (event.data === "ping" || event.data === "连接成功") {
          return;
        }

        // 处理重新评审消息
        if (event.data === "reEvaluation" || event.data === "重新评审") {
          this.handleReEvaluationMessage();
          return;
        }

        // 尝试解析JSON消息
        try {
          const data = JSON.parse(event.data);
          
          // 处理流标通知
          if (data.type === "flowBidNotification") {
            this.handleFlowBidNotification(data);
            return;
          }
          
          // 处理重新评审消息
          if (data.type === "reEvaluation" || data.messageType === "reEvaluation") {
            this.handleReEvaluationMessage(data);
          }
        } catch (parseError) {
          // 如果不是JSON格式，检查是否是重新评审相关的字符串消息
          if (event.data.includes("reEvaluation") || event.data.includes("重新评审")) {
            this.handleReEvaluationMessage();
          }
        }
      } catch (error) {
        console.error("处理WebSocket消息失败:", error);
      }
    },

    /**
     * WebSocket连接关闭事件
     */
    onWebSocketClose(event) {
      console.log("专家评审WebSocket连接已关闭");
      this.stopHeartbeat();
      
      // 如果不是正常关闭，尝试重连
      if (!event.wasClean) {
        this.scheduleReconnect();
      }
    },

    /**
     * WebSocket连接错误事件
     */
    onWebSocketError(error) {
      console.error("专家评审WebSocket连接错误:", error);
      this.scheduleReconnect();
    },

    /**
     * 处理重新评审消息
     */
    handleReEvaluationMessage(data = null) {
      console.log("收到重新评审通知", data);

      // 显示提示消息
      this.$message.info("收到重新评审通知，页面即将刷新");

      // 延迟1秒后刷新页面状态
      setTimeout(() => {
        this.refreshPageAfterReEvaluation();
      }, 1000);
    },

    /**
     * 处理专家信息更新消息
     */
    handleExpertInfoUpdateMessage(data = null) {
      console.log("收到专家信息更新通知", data);

      // 显示提示消息
      this.$message.success("专家信息已更新");

      // 刷新页面中的专家信息
      this.refreshExpertInfo();
    },

    /**
     * 处理分值修改消息
     */
    handleScoreUpdateMessage(data = null) {
      console.log("收到分值修改通知", data);

      // 显示提示消息
      this.$message.info("评审分值已更新，页面即将刷新");

      // 延迟1秒后刷新页面数据
      setTimeout(() => {
        this.refreshPageAfterScoreUpdate();
      }, 1000);
    },

    /**
     * 处理流标通知
     */
    handleFlowBidNotification(data) {
      console.log("收到流标通知", data);
      
      // 显示提示消息
      this.$message.info(data.message || "项目已流标，请前往评审结束页面");
      
      // 跳转到评审结束页面
      this.$router.push({
        path: "/expertReview/end",
        query: {
          projectId: data.projectId,
          zjhm: this.$route.query.zjhm,
          hasPrintedReport: false,
          flowBidStatus: 1 // 标记为已流标
        }
      });
    },

    /**
     * 重新评审后刷新页面状态
     */
    refreshPageAfterReEvaluation() {
      try {
        // 如果当前页面有init方法，调用它来刷新数据
        if (typeof this.init === 'function') {
          this.init();
        }

        // 如果当前页面有getEvalExpertStatus方法，调用它来刷新专家状态
        if (typeof this.getEvalExpertStatus === 'function') {
          this.getEvalExpertStatus();
        }

        // 重置到第一步
        if (this.node !== undefined) {
          this.node = "one";
        }

        // 增强刷新：在接下来的30秒内每2秒刷新一次，确保状态同步
        this.enhancedRefresh();

        console.log("页面状态已刷新");
      } catch (error) {
        console.error("刷新页面状态失败:", error);
      }
    },

    /**
     * 刷新专家信息
     */
    refreshExpertInfo() {
      try {
        // 如果当前页面有initExpertInfo方法，调用它来刷新专家信息
        if (typeof this.initExpertInfo === 'function') {
          this.initExpertInfo();
        }

        // 如果当前页面有expertInfo属性，从localStorage重新获取
        if (this.expertInfo !== undefined) {
          this.expertInfo = JSON.parse(localStorage.getItem("expertInfo") || "{}");
        }

        // 如果当前页面有localExpertInfo属性，也需要更新
        if (this.localExpertInfo !== undefined) {
          this.localExpertInfo = JSON.parse(localStorage.getItem("expertInfo") || "{}");
        }

        console.log("专家信息已刷新");
      } catch (error) {
        console.error("刷新专家信息失败:", error);
      }
    },

    /**
     * 增强刷新机制
     * 在重新评审后短时间内更频繁地刷新页面状态
     */
    enhancedRefresh() {
      let refreshCount = 0;
      const maxRefreshCount = 15; // 30秒内刷新15次，每2秒一次

      const enhancedTimer = setInterval(() => {
        refreshCount++;

        // 刷新页面数据
        if (typeof this.init === 'function') {
          this.init();
        }

        if (typeof this.getEvalExpertStatus === 'function') {
          this.getEvalExpertStatus();
        }

        // 达到最大刷新次数后停止
        if (refreshCount >= maxRefreshCount) {
          clearInterval(enhancedTimer);
          console.log("增强刷新已完成");
        }
      }, 2000); // 每2秒刷新一次
    },

    /**
     * 开始心跳检测
     */
    startHeartbeat() {
      this.stopHeartbeat();
      this.heartbeatTimer = setInterval(() => {
        if (this.reviewWebSocket && this.reviewWebSocket.readyState === WebSocket.OPEN) {
          this.reviewWebSocket.send("ping");
        }
      }, 30000); // 30秒发送一次心跳
    },

    /**
     * 停止心跳检测
     */
    stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },

    /**
     * 安排重连
     */
    scheduleReconnect() {
      if (this.reconnectCount >= this.maxReconnectCount) {
        console.error("WebSocket重连次数已达上限，停止重连");
        return;
      }

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      const delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000);
      console.log(`${delay}ms后尝试重连WebSocket (第${this.reconnectCount + 1}次)`);
      
      this.reconnectTimer = setTimeout(() => {
        this.reconnectCount++;
        this.connectReviewWebSocket();
      }, delay);
    },

    /**
     * 关闭WebSocket连接
     */
    closeReviewWebSocket() {
      this.stopHeartbeat();

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      if (this.reviewWebSocket) {
        this.reviewWebSocket.close();
        this.reviewWebSocket = null;
      }

      // 移除本地存储监听器
      if (this.storageListener) {
        window.removeEventListener('storage', this.storageListener);
        this.storageListener = null;
      }
    },

    /**
     * 分值修改后刷新页面状态
     */
    refreshPageAfterScoreUpdate() {
      try {
        // 如果当前页面有init方法，调用它来刷新数据
        if (typeof this.init === 'function') {
          this.init();
        }

        console.log("分值修改后页面状态已刷新");
      } catch (error) {
        console.error("刷新页面状态失败:", error);
      }
    },

    /**
     * 触发重新评审通知
     * 在专家组长发起重新评审后调用此方法
     */
    triggerReEvaluationNotification() {
      const projectId = this.$route.query.projectId;
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo") || "{}");

      const notificationData = {
        type: 'reEvaluation',
        projectId: projectId,
        expertId: expertInfo.resultId,
        timestamp: Date.now(),
        message: '专家组长发起了重新评审'
      };

      // 存储到localStorage，触发其他页面的监听器
      localStorage.setItem('reEvaluationNotification', JSON.stringify(notificationData));

      // 立即清除，避免影响后续操作
      setTimeout(() => {
        localStorage.removeItem('reEvaluationNotification');
      }, 1000);
    },

    /**
     * 触发分值修改通知
     * 在专家修改分值后调用此方法
     */
    triggerScoreUpdateNotification() {
      const projectId = this.$route.query.projectId;
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo") || "{}");

      const notificationData = {
        type: 'scoreUpdate',
        projectId: projectId,
        expertId: expertInfo.resultId,
        timestamp: Date.now(),
        message: '专家修改了评审分值'
      };

      // 存储到localStorage，触发其他页面的监听器
      localStorage.setItem('scoreUpdateNotification', JSON.stringify(notificationData));

      // 立即清除，避免影响后续操作
      setTimeout(() => {
        localStorage.removeItem('scoreUpdateNotification');
      }, 1000);
    },
  },

  /**
   * 组件挂载时初始化WebSocket连接
   */
  mounted() {
    // 延迟初始化，确保页面数据加载完成
    this.$nextTick(() => {
      setTimeout(() => {
        this.initReviewWebSocket();
      }, 1000);
    });
  },

  /**
   * 组件销毁前关闭WebSocket连接
   */
  beforeDestroy() {
    this.closeReviewWebSocket();
  },
};
