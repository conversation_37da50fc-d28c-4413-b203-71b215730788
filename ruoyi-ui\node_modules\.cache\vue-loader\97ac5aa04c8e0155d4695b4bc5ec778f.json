{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summaryConfirm\\table.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summaryConfirm\\table.vue", "mtime": 1753958157918}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQovL+i/memHjOWPr+S7peWvvOWFpeWFtuS7luaWh+S7tu+8iOavlOWmgu+8mue7hOS7tu+8jOW3peWFt2pz77yM56ys5LiJ5pa55o+S5Lu2anPvvIxqc29u5paH5Lu277yM5Zu+54mH5paH5Lu2562J562J77yJDQovL+S+i+Wmgu+8mmltcG9ydCDjgIrnu4Tku7blkI3np7DjgIsgZnJvbSAn44CK57uE5Lu26Lev5b6E44CLJzsNCmltcG9ydCB7IHVwZGF0ZURldGFpbCB9IGZyb20gIkAvYXBpL2V2YWx1YXRpb24vZGV0YWlsIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAvL2ltcG9ydOW8leWFpeeahOe7hOS7tumcgOimgeazqOWFpeWIsOWvueixoeS4reaJjeiDveS9v+eUqA0KICBjb21wb25lbnRzOiB7fSwNCiAgZGF0YSgpIHsNCiAgICAvL+i/memHjOWtmOaUvuaVsOaNrg0KICAgIHJldHVybiB7DQogICAgICBmb3JtOiB7DQogICAgICAgIHN1cHBsaWVyOiAiIiwNCiAgICAgICAgZmFjdG9yczogIiIsDQogICAgICAgIG1vZGlmaWVkOiAiIiwNCiAgICAgICAgcmVhc29uOiAiIiwNCiAgICAgICAgc2NvcmVMZXZlbDogIiIsDQogICAgICB9LA0KICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLA0KICAgICAgaGVhZFN0eWxlT25lOiB7DQogICAgICAgICJmb250LWZhbWlseSI6ICJTb3VyY2VIYW5TYW5zU0MtQm9sZCIsDQogICAgICAgICJmb250LXdlaWdodCI6ICI3MDAiLA0KICAgICAgICAiZm9udC1zaXplIjogIjE4cHgiLA0KICAgICAgICBiYWNrZ3JvdW5kOiAiI2ZmZiIsDQogICAgICAgIGNvbG9yOiAiIzMzMzMzMyIsDQogICAgICB9LA0KICAgICAgaGVhZFN0eWxlOiB7DQogICAgICAgICJ0ZXh0LWFsaWduIjogImNlbnRlciIsDQogICAgICAgICJmb250LWZhbWlseSI6ICJTb3VyY2VIYW5TYW5zU0MtQm9sZCIsDQogICAgICAgIGJhY2tncm91bmQ6ICIjMTc2QURCIiwNCiAgICAgICAgY29sb3I6ICIjZmZmIiwNCiAgICAgICAgImZvbnQtc2l6ZSI6ICIxNnB4IiwNCiAgICAgICAgImZvbnQtd2VpZ2h0IjogIjcwMCIsDQogICAgICAgIGJvcmRlcjogIjAiLA0KICAgICAgfSwNCiAgICAgIGNlbGxTdHlsZTogew0KICAgICAgICAidGV4dC1hbGlnbiI6ICJjZW50ZXIiLA0KICAgICAgICAiZm9udC1mYW1pbHkiOiAiU291cmNlSGFuU2Fuc1NDLUJvbGQiLA0KICAgICAgICBoZWlnaHQ6ICI2MHB4IiwNCiAgICAgICAgY29sb3I6ICIjMDAwIiwNCiAgICAgICAgImZvbnQtc2l6ZSI6ICIxNHB4IiwNCiAgICAgICAgImZvbnQtd2VpZ2h0IjogIjcwMCIsDQogICAgICB9LA0KICAgIH07DQogIH0sDQogIHByb3BzOiB7DQogICAgbGFiZWw6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICIiLA0KICAgIH0sDQogICAgdGFibGVEYXRhOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6IFtdLA0KICAgIH0sDQogICAgY29sdW1uOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6IFtdLA0KICAgIH0sDQogIH0sDQoNCiAgLy/nm5HlkKzlsZ7mgKcg57G75Ly85LqOZGF0YeamguW/tQ0KICBjb21wdXRlZDoge30sDQogIC8v55uR5o6nZGF0YeS4reeahOaVsOaNruWPmOWMlg0KICB3YXRjaDoge30sDQogIC8v5pa55rOV6ZuG5ZCIDQogIG1ldGhvZHM6IHsNCiAgICAvLyDnvJbovpENCiAgICBlZGl0KGl0ZW0sIHJvdykgew0KICAgICAgdGhpcy5mb3JtLnN1cHBsaWVyID0gaXRlbTsNCiAgICAgIHRoaXMuZm9ybS5mYWN0b3JzID0gcm93WyJmYWN0b3IiXTsNCiAgICAgIHRoaXMuZm9ybS5tb2RpZmllZCA9IHJvd1tpdGVtXS5ldmFsdWF0aW9uUmVzdWx0Ow0KICAgICAgdGhpcy5mb3JtLnJlYXNvbiA9ICIiOw0KICAgICAgdGhpcy5mb3JtLmV4cGVydEV2YWx1YXRpb25JZCA9IHJvd1tpdGVtXS5leHBlcnRFdmFsdWF0aW9uSWQ7DQogICAgICB0aGlzLmZvcm0uc2NvcmVMZXZlbCA9IHJvd1tpdGVtXS5zY29yZUxldmVsOw0KICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvLyDnoa7orqQNCiAgICBjb25maXJtKCkgew0KICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgZXhwZXJ0RXZhbHVhdGlvbklkOiB0aGlzLmZvcm0uZXhwZXJ0RXZhbHVhdGlvbklkLA0KICAgICAgICBldmFsdWF0aW9uUmVzdWx0OiB0aGlzLmZvcm0ubW9kaWZpZWQsDQogICAgICAgIGV2YWx1YXRpb25SZW1hcms6IHRoaXMuZm9ybS5yZWFzb24sDQogICAgICB9Ow0KICAgICAgdXBkYXRlRGV0YWlsKGRhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgIHRoaXMuJGVtaXQoInVwZGF0ZSIsICLmm7TmlrAiKTsNCg0KICAgICAgICAgIC8vIOinpuWPkeWIhuWAvOS/ruaUuemAmuefpe+8jOmAmuefpeWFtuS7lumhtemdou+8iOWmguivhOWuoeaxh+aAu+mhtemdou+8iQ0KICAgICAgICAgIGlmICh0aGlzLiRwYXJlbnQgJiYgdHlwZW9mIHRoaXMuJHBhcmVudC50cmlnZ2VyU2NvcmVVcGRhdGVOb3RpZmljYXRpb24gPT09ICdmdW5jdGlvbicpIHsNCiAgICAgICAgICAgIHRoaXMuJHBhcmVudC50cmlnZ2VyU2NvcmVVcGRhdGVOb3RpZmljYXRpb24oKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICB0YWJsZUhlYWRlckNsYXNzKHsgcm93LCBjb2x1bW4sIHJvd0luZGV4LCBjb2x1bW5JbmRleCB9KSB7DQogICAgICByZXR1cm4gcm93SW5kZXggPT0gMCAmJiBjb2x1bW5JbmRleCA9PSAwDQogICAgICAgID8gdGhpcy5oZWFkU3R5bGVPbmUNCiAgICAgICAgOiB0aGlzLmhlYWRTdHlsZTsNCiAgICB9LA0KICB9LA0KICAvL+eUn+WRveWRqOacnyAtIOWIm+W7uuWujOaIkO+8iOWPr+S7peiuv+mXruW9k+WJjXRoaXPlrp7kvovvvIkNCiAgY3JlYXRlZCgpIHt9LA0KICAvL+eUn+WRveWRqOacnyAtIOaMgui9veWujOaIkO+8iOWPr+S7peiuv+mXrkRPTeWFg+e0oO+8iQ0KICBtb3VudGVkKCkge30sDQogIGJlZm9yZUNyZWF0ZSgpIHt9LCAvL+eUn+WRveWRqOacnyAtIOWIm+W7uuS5i+WJjQ0KICBiZWZvcmVNb3VudCgpIHt9LCAvL+eUn+WRveWRqOacnyAtIOaMgui9veS5i+WJjQ0KICBiZWZvcmVVcGRhdGUoKSB7fSwgLy/nlJ/lkb3lkajmnJ8gLSDmm7TmlrDkuYvliY0NCiAgdXBkYXRlZCgpIHt9LCAvL+eUn+WRveWRqOacnyAtIOabtOaWsOS5i+WQjg0KICBiZWZvcmVEZXN0cm95KCkge30sIC8v55Sf5ZG95ZGo5pyfIC0g6ZSA5q+B5LmL5YmNDQogIGRlc3Ryb3llZCgpIHt9LCAvL+eUn+WRveWRqOacnyAtIOmUgOavgeWujOaIkA0KICBhY3RpdmF0ZWQoKSB7fSwgLy/lpoLmnpzpobXpnaLmnIlrZWVwLWFsaXZl57yT5a2Y5Yqf6IO977yM6L+Z5Liq5Ye95pWw5Lya6Kem5Y+RDQp9Ow0K"}, {"version": 3, "sources": ["table.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuHA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "table.vue", "sourceRoot": "src/views/expertReview/summaryConfirm", "sourcesContent": ["<!-- 专家复核表格 -->\r\n<template>\r\n  <div>\r\n    <el-table\r\n      :data=\"tableData\"\r\n      style=\"width: 100%\"\r\n      :cell-style=\"cellStyle\"\r\n      :header-cell-style=\"tableHeaderClass\"\r\n    >\r\n      <el-table-column\r\n        header-align=\"center\"\r\n        :label=\"label\"\r\n      >\r\n        <el-table-column\r\n          prop=\"factor\"\r\n          width=\"300\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-for=\"(item, index) in column\"\r\n          :key=\"index\"\r\n          :prop=\"item\"\r\n          :label=\"item\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"display: flex;justify-content: center;align-items: center;color:#176ADB\">\r\n              <div v-if=\"label == '资格性评审' || label == '符合性评审'\">\r\n                <span v-if=\"scope.row[item].evaluationResult == 0 || scope.row[item].evaluationResult == null || scope.row[item].evaluationResult==undefined\">未通过</span>\r\n                <span v-else>通过</span>\r\n              </div>\r\n              <div v-else>\r\n                <span>{{ scope.row[item].evaluationResult }}</span>\r\n              </div>\r\n              <div\r\n                style=\"cursor: pointer;margin-left:10px\"\r\n                @click=\"edit(item,scope.row)\"\r\n              >\r\n                <svg-icon\r\n                  icon-class=\"edit\"\r\n                  class-name=\"edit\"\r\n                />\r\n              </div>\r\n\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-dialog\r\n      title=\"评审内容修改\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n    >\r\n      <div class=\"content\">\r\n        <div class=\"item\">\r\n          <div class=\"lable\">供应商：</div> {{ form.supplier }}\r\n        </div>\r\n        <div class=\"item\">\r\n          <div class=\"lable\">评审因素：</div> {{ form.factors }}\r\n        </div>\r\n        <div\r\n          class=\"item\"\r\n          v-if=\"label == '资格性评审' || label == '符合性评审'\"\r\n        >\r\n          <div class=\"lable\">修改值：</div>\r\n          <el-switch\r\n            v-model=\"form.modified\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n          >\r\n          </el-switch>\r\n        </div>\r\n        <div\r\n          class=\"item\"\r\n          v-else\r\n        >\r\n          <div class=\"lable\">修改值：</div>\r\n          <el-select\r\n            v-model=\"form.modified\"\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"(score , index) in form.scoreLevel.split(',')\"\r\n              :key=\"index\"\r\n              :label=\"score\"\r\n              :value=\"score\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"item\">\r\n          <div class=\"lable\">修改原因：</div>\r\n          <div style=\"width:80%\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"请输入内容\"\r\n              v-model=\"form.reason\"\r\n            >\r\n            </el-input>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n\r\n      <div\r\n        slot=\"footer\"\r\n        class=\"dialog-footer\"\r\n      >\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"confirm\"\r\n        >确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\r\n//例如：import 《组件名称》 from '《组件路径》';\r\nimport { updateDetail } from \"@/api/evaluation/detail\";\r\n\r\nexport default {\r\n  //import引入的组件需要注入到对象中才能使用\r\n  components: {},\r\n  data() {\r\n    //这里存放数据\r\n    return {\r\n      form: {\r\n        supplier: \"\",\r\n        factors: \"\",\r\n        modified: \"\",\r\n        reason: \"\",\r\n        scoreLevel: \"\",\r\n      },\r\n      dialogFormVisible: false,\r\n      headStyleOne: {\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        \"font-weight\": \"700\",\r\n        \"font-size\": \"18px\",\r\n        background: \"#fff\",\r\n        color: \"#333333\",\r\n      },\r\n      headStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        background: \"#176ADB\",\r\n        color: \"#fff\",\r\n        \"font-size\": \"16px\",\r\n        \"font-weight\": \"700\",\r\n        border: \"0\",\r\n      },\r\n      cellStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        height: \"60px\",\r\n        color: \"#000\",\r\n        \"font-size\": \"14px\",\r\n        \"font-weight\": \"700\",\r\n      },\r\n    };\r\n  },\r\n  props: {\r\n    label: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    tableData: {\r\n      type: Array,\r\n      default: [],\r\n    },\r\n    column: {\r\n      type: Array,\r\n      default: [],\r\n    },\r\n  },\r\n\r\n  //监听属性 类似于data概念\r\n  computed: {},\r\n  //监控data中的数据变化\r\n  watch: {},\r\n  //方法集合\r\n  methods: {\r\n    // 编辑\r\n    edit(item, row) {\r\n      this.form.supplier = item;\r\n      this.form.factors = row[\"factor\"];\r\n      this.form.modified = row[item].evaluationResult;\r\n      this.form.reason = \"\";\r\n      this.form.expertEvaluationId = row[item].expertEvaluationId;\r\n      this.form.scoreLevel = row[item].scoreLevel;\r\n      this.dialogFormVisible = true;\r\n    },\r\n    // 确认\r\n    confirm() {\r\n      const data = {\r\n        expertEvaluationId: this.form.expertEvaluationId,\r\n        evaluationResult: this.form.modified,\r\n        evaluationRemark: this.form.reason,\r\n      };\r\n      updateDetail(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.dialogFormVisible = false;\r\n          this.$message.success(\"修改成功\");\r\n          this.$emit(\"update\", \"更新\");\r\n\r\n          // 触发分值修改通知，通知其他页面（如评审汇总页面）\r\n          if (this.$parent && typeof this.$parent.triggerScoreUpdateNotification === 'function') {\r\n            this.$parent.triggerScoreUpdateNotification();\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    tableHeaderClass({ row, column, rowIndex, columnIndex }) {\r\n      return rowIndex == 0 && columnIndex == 0\r\n        ? this.headStyleOne\r\n        : this.headStyle;\r\n    },\r\n  },\r\n  //生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  //生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {},\r\n  beforeCreate() {}, //生命周期 - 创建之前\r\n  beforeMount() {}, //生命周期 - 挂载之前\r\n  beforeUpdate() {}, //生命周期 - 更新之前\r\n  updated() {}, //生命周期 - 更新之后\r\n  beforeDestroy() {}, //生命周期 - 销毁之前\r\n  destroyed() {}, //生命周期 - 销毁完成\r\n  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发\r\n};\r\n</script>\r\n<style>\r\n.custom-header {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  background: #fff;\r\n  color: red;\r\n}\r\n.content {\r\n  width: 80%;\r\n}\r\n.item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n.lable {\r\n  width: 120px;\r\n}\r\n</style>"]}]}