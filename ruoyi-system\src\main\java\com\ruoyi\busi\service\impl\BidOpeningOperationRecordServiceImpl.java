package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.BidOpeningOperationRecord;
import com.ruoyi.busi.domain.BusiBidOpening;
import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.mapper.BidOpeningOperationRecordMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 开标操作记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Service
public class BidOpeningOperationRecordServiceImpl extends ServiceImpl<BidOpeningOperationRecordMapper, BidOpeningOperationRecord> implements IBidOpeningOperationRecordService {
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiTenderProjectService tenderProjectService;
    @Autowired
    private IBusiBidOpeningService iBusiBidOpeningService;
    @Autowired
    private IBusiBiddingRecordService busiBiddingRecordService;

    /**
     * 查询开标操作记录列表
     *
     * @param bidOpeningOperationRecord 开标操作记录
     * @return 开标操作记录
     */
    @Override
    public List<BidOpeningOperationRecord> selectList(BidOpeningOperationRecord bidOpeningOperationRecord) {
        QueryWrapper<BidOpeningOperationRecord> bidOpeningOperationRecordQueryWrapper = new QueryWrapper<>();
        bidOpeningOperationRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(bidOpeningOperationRecord.getProjectId()), "project_id", bidOpeningOperationRecord.getProjectId());
        bidOpeningOperationRecordQueryWrapper.eq(ObjectUtil.isNotEmpty(bidOpeningOperationRecord.getOperationType()), "operation_type", bidOpeningOperationRecord.getOperationType());
        String beginOperationTime = bidOpeningOperationRecord.getParams().get("beginOperationTime") != null ? bidOpeningOperationRecord.getParams().get("beginOperationTime") + "" : "";
        String endOperationTime = bidOpeningOperationRecord.getParams().get("endOperationTime") + "" != null ? bidOpeningOperationRecord.getParams().get("endOperationTime") + "" : "";
        bidOpeningOperationRecordQueryWrapper.between(ObjectUtil.isNotEmpty(beginOperationTime) && ObjectUtil.isNotEmpty(endOperationTime), "operation_time", beginOperationTime, endOperationTime);
        String beginDecryptionTime = bidOpeningOperationRecord.getParams().get("beginDecryptionTime") != null ? bidOpeningOperationRecord.getParams().get("beginDecryptionTime") + "" : "";
        String endDecryptionTime = bidOpeningOperationRecord.getParams().get("endDecryptionTime") + "" != null ? bidOpeningOperationRecord.getParams().get("endDecryptionTime") + "" : "";
        bidOpeningOperationRecordQueryWrapper.between(ObjectUtil.isNotEmpty(beginDecryptionTime) && ObjectUtil.isNotEmpty(endDecryptionTime), "decryption_time", beginDecryptionTime, endDecryptionTime);
        bidOpeningOperationRecordQueryWrapper.apply(
                ObjectUtil.isNotEmpty(bidOpeningOperationRecord.getParams().get("dataScope")),
                bidOpeningOperationRecord.getParams().get("dataScope") + ""
        );

        List<BidOpeningOperationRecord> list = list(bidOpeningOperationRecordQueryWrapper);
        // 定义日期格式化对象，用于将Date类型时间格式化为指定格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (BidOpeningOperationRecord record : list) {
            // 获取当前系统时间
            Date currentDate = new Date();
            // 格式化为指定格式的字符串
            String systemTime = sdf.format(currentDate);
            // 设置到每个BidOpeningOperationRecord对象中（假设类中有对应的setSystemTime方法）
            record.setSystemTime(systemTime);
        }
        return list;
    }

    @Override
    public AjaxResult getProjectStatus(BidOpeningOperationRecord bidOpeningOperationRecord, LoginUser loginUser) {
        BidOpeningOperationRecord returnEntity = new BidOpeningOperationRecord();
        returnEntity.setProjectId(bidOpeningOperationRecord.getProjectId());
        BusiTenderProject byId = tenderProjectService.getById(bidOpeningOperationRecord.getProjectId());
        returnEntity.setProject(byId);
        //获取供应商是否已签到
     /*   BusiBidderInfo supplierBidderInfo ;
        if (loginUser.getUser().getRoles().get(0).getRoleKey().equals("supplier")){
            supplierBidderInfo = busiBidderInfoService.getOne(new QueryWrapper<BusiBidderInfo>()
                    .eq("project_id", bidOpeningOperationRecord.getProjectId())
                    .eq("bidder_id", bidOpeningOperationRecord.getUserId())
                    .eq("del_flag", 0));*/

        List<BidOpeningOperationRecord> list = this.list(new QueryWrapper<BidOpeningOperationRecord>().eq("project_id", byId.getProjectId()));
        if (!list.isEmpty()) {
            //取type值最大的一条
            Optional<BidOpeningOperationRecord> maxByOperationType = list.stream()
                    .max((record1, record2) -> record1.getOperationType().compareTo(record2.getOperationType()));
            maxByOperationType.ifPresent(maxRecord -> {
                // 使用maxRecord
                System.out.println("Record with max operationType: " + maxRecord.getOperationType());
                List<SysDictData> bidOpeningOperationType = DictUtils.getDictCache("bid_opening_operation_type");
                if (bidOpeningOperationType != null) {
                    boolean containsOperationType = containsDictValue(bidOpeningOperationType, maxRecord.getOperationType() + "");
                    if (containsOperationType) {
                        for (SysDictData sysDictData : bidOpeningOperationType) {
                            System.out.println(sysDictData.getDictLabel());
                            System.out.println(sysDictData.getDictValue());
                            switch (Integer.parseInt(maxRecord.getOperationType() + "")) {
                                case 1:
                                    //点击开始开标之后
                                    // 代理机构跳转至投标人公示页面
                                    returnEntity.setAgencyPageStatus(2);
                                    //if (loginUser.getUser().getRoles().get(0).getRoleKey().equals("supplier")){

                                    //供应商签到后默认进入开标准备页面
                                    if (loginUser.getUser().getRoles().get(0).getRoleKey().equals("supplier")) {

                                        BusiBidderInfo supplierBidderInfo = busiBidderInfoService.getOne(new QueryWrapper<BusiBidderInfo>()
                                                .eq("project_id", bidOpeningOperationRecord.getProjectId())
                                                .eq("bidder_id", loginUser.getEntId())
                                                .eq("del_flag", 0));
                                        if (supplierBidderInfo.getSignInStatus() == 1) {
                                            returnEntity.setSupplierPageStatus(1);
                                        } else {
                                            returnEntity.setSupplierPageStatus(0);
                                        }
                                    } else {
                                        returnEntity.setSupplierPageStatus(0);
                                    }
                                    break;
                                case 2:
                                    //点击投标人公示按钮，代理机构进入标书解密，
                                    returnEntity.setAgencyPageStatus(3);
                                    //供应商进入，投标人公示页面
                                    returnEntity.setSupplierPageStatus(2);
                                    break;
                                case 3:
                                    //点击投标人公示按钮，代理机构进入标书解密，
                                    returnEntity.setAgencyPageStatus(3);
                                    //供应商进入，投标人公示页面
                                    returnEntity.setSupplierPageStatus(3);
                                    break;
                                case 4:
                                    //代理机构点击解密页面的下一步按钮，进入唱标页面
                                    returnEntity.setAgencyPageStatus(5);
                                    returnEntity.setSupplierPageStatus(3);
                                    break;
                                case 5:
                                    //唱标  代理机构是开标结束页面
                                    returnEntity.setAgencyPageStatus(5);
                                    returnEntity.setSupplierPageStatus(4);
                                    break;
                                case 6:
                                    //点击开标结束按钮，代理机构和供应商都是开标结束，
                                    returnEntity.setAgencyPageStatus(5);
                                    returnEntity.setSupplierPageStatus(5);
                                    break;
                                default:
                                    // 默认情况，如果没有匹配的 case
                                    returnEntity.setSupplierPageStatus(0);
                                    //代理机构进入首页，即开标准备页面
                                    returnEntity.setAgencyPageStatus(1);
                                    break;
                            }
                        }
                    } else {
                        throw new RuntimeException("字典值不存在，请联系管理员检查流程配置");
                    }
                }
            });
        } else {
            List<BusiBidderInfo> list1 = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("bidder_id", loginUser.getEntId()).eq("project_id", byId.getProjectId()));
            if (loginUser.getUser().getRoles().get(0).getRoleKey().equals("supplier")) {
                if (list1.size()>0){
                    returnEntity.setSupplierPageStatus(list1.get(0).getSignInStatus());
                }else {
                    returnEntity.setSupplierPageStatus(0);

                }
                returnEntity.setAgencyPageStatus(1);

            }
        }
        // }
        //   return  AjaxResult.success(returnEntity);
        return AjaxResult.success(returnEntity);
    }

    @Override
    public AjaxResult saveRecord(BidOpeningOperationRecord bidOpeningOperationRecord) {
        BidOpeningOperationRecord r = getOne(new QueryWrapper<BidOpeningOperationRecord>()
                .eq("project_id", bidOpeningOperationRecord.getProjectId())
                .eq("operation_type", bidOpeningOperationRecord.getOperationType()));
        if(r!=null){
            bidOpeningOperationRecord.setOperationId(r.getOperationId());
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        bidOpeningOperationRecord.setCreateBy(user.getUserName());

        bidOpeningOperationRecord.setUserId(user.getEntId());
        if (bidOpeningOperationRecord.getOperationType() == 6) {
            try {
                busiBiddingRecordService.updateAmountByProject(bidOpeningOperationRecord.getProjectId());
                //生成开标记录表，附件
                busiBidderInfoService.exportBidOpeningRecords(bidOpeningOperationRecord.getProjectId());
                //
                BusiBidOpening busiBidOpening = iBusiBidOpeningService.getOne(new QueryWrapper<BusiBidOpening>()
                        .eq("project_id", bidOpeningOperationRecord.getProjectId())
                );
                busiBidOpening.setBidOpeningEndTime(bidOpeningOperationRecord.getOperationTime());
                iBusiBidOpeningService.saveWithRecord(busiBidOpening);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else if (bidOpeningOperationRecord.getOperationType() == 1){
            BusiTenderProject project = tenderProjectService.getById(bidOpeningOperationRecord.getProjectId());
            BusiBidOpening busiBidOpening=new BusiBidOpening();
            busiBidOpening.setProjectId(project.getProjectId());
            busiBidOpening.setProjectName(project.getProjectName());
            busiBidOpening.setProjectCode(project.getProjectCode());
            busiBidOpening.setBidOpeningTime(bidOpeningOperationRecord.getOperationTime());
            busiBidOpening.setDelFlag(0);
            iBusiBidOpeningService.save(busiBidOpening);
        }else if (bidOpeningOperationRecord.getOperationType() == 3){
            // 获取当前时间
            Date currentDate = new Date();
            // 创建Calendar实例，并设置为当前时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            // 指定要增加的分钟数，这里以增加30分钟为例，你可以替换为任意想要增加的分钟数值
            int minutesToAdd = bidOpeningOperationRecord.getMinutes();
            calendar.add(Calendar.MINUTE, minutesToAdd);
            // 获取增加分钟数后的Date类型时间
            Date newDate = calendar.getTime();
            bidOpeningOperationRecord.setDecryptionTime(newDate);
            List<BidOpeningOperationRecord> list = list(new QueryWrapper<BidOpeningOperationRecord>()
                    .eq("project_id", bidOpeningOperationRecord.getProjectId())
                    .eq("operation_type", bidOpeningOperationRecord.getOperationType())
            );
            // 注释掉原来的限制，允许重新设置解密时间
            // if (list.size() > 0) {
            //     return AjaxResult.error("已经点过解密了，只能解密一次");
            // }
        }
        saveOrUpdate(bidOpeningOperationRecord);
        // 定义日期格式化对象，用于将Date类型时间格式化为指定格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 格式化为指定格式的字符串
        String systemTime = sdf.format(new Date());
        // 设置到每个BidOpeningOperationRecord对象中（假设类中有对应的setSystemTime方法）
        bidOpeningOperationRecord.setSystemTime(systemTime);
        return AjaxResult.success(bidOpeningOperationRecord);
    }

    public static boolean containsDictValue(List<SysDictData> list, String operationType) {
        // 使用Stream API来检查集合中是否有DictValue包含operationType
        return list.stream()
                .map(SysDictData::getDictValue) // 获取每个SysDictData的DictValue
                .anyMatch(dictValue -> dictValue.contains(operationType)); // 检查是否包含operationType
    }

    @Override
    public AjaxResult getSignInCount(Long projectId) {
        // 查询已签到的供应商数量
        QueryWrapper<BusiBidderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId)
                   .eq("sign_in_status", 1)
                   .eq("del_flag", 0);
        
        long signedCount = busiBidderInfoService.count(queryWrapper);
        
        // 查询项目下所有供应商数量
        QueryWrapper<BusiBidderInfo> allQueryWrapper = new QueryWrapper<>();
        allQueryWrapper.eq("project_id", projectId)
                      .eq("del_flag", 0);
        
        long totalCount = busiBidderInfoService.count(allQueryWrapper);
        
        // 返回统计结果
        return AjaxResult.success()
                .put("signedCount", signedCount)
                .put("totalCount", totalCount)
                .put("projectId", projectId);
    }
}
