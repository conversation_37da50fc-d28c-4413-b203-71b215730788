{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summaryConfirm\\table.vue?vue&type=style&index=0&id=689519dc&lang=css", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\summaryConfirm\\table.vue", "mtime": 1753958157918}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouY3VzdG9tLWhlYWRlciB7DQogIGZvbnQtZmFtaWx5OiBTb3VyY2VIYW5TYW5zU0MtQm9sZDsNCiAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBjb2xvcjogcmVkOw0KfQ0KLmNvbnRlbnQgew0KICB3aWR0aDogODAlOw0KfQ0KLml0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQp9DQoubGFibGUgew0KICB3aWR0aDogMTIwcHg7DQp9DQo="}, {"version": 3, "sources": ["table.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "table.vue", "sourceRoot": "src/views/expertReview/summaryConfirm", "sourcesContent": ["<!-- 专家复核表格 -->\r\n<template>\r\n  <div>\r\n    <el-table\r\n      :data=\"tableData\"\r\n      style=\"width: 100%\"\r\n      :cell-style=\"cellStyle\"\r\n      :header-cell-style=\"tableHeaderClass\"\r\n    >\r\n      <el-table-column\r\n        header-align=\"center\"\r\n        :label=\"label\"\r\n      >\r\n        <el-table-column\r\n          prop=\"factor\"\r\n          width=\"300\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          v-for=\"(item, index) in column\"\r\n          :key=\"index\"\r\n          :prop=\"item\"\r\n          :label=\"item\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"display: flex;justify-content: center;align-items: center;color:#176ADB\">\r\n              <div v-if=\"label == '资格性评审' || label == '符合性评审'\">\r\n                <span v-if=\"scope.row[item].evaluationResult == 0 || scope.row[item].evaluationResult == null || scope.row[item].evaluationResult==undefined\">未通过</span>\r\n                <span v-else>通过</span>\r\n              </div>\r\n              <div v-else>\r\n                <span>{{ scope.row[item].evaluationResult }}</span>\r\n              </div>\r\n              <div\r\n                style=\"cursor: pointer;margin-left:10px\"\r\n                @click=\"edit(item,scope.row)\"\r\n              >\r\n                <svg-icon\r\n                  icon-class=\"edit\"\r\n                  class-name=\"edit\"\r\n                />\r\n              </div>\r\n\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-dialog\r\n      title=\"评审内容修改\"\r\n      :visible.sync=\"dialogFormVisible\"\r\n    >\r\n      <div class=\"content\">\r\n        <div class=\"item\">\r\n          <div class=\"lable\">供应商：</div> {{ form.supplier }}\r\n        </div>\r\n        <div class=\"item\">\r\n          <div class=\"lable\">评审因素：</div> {{ form.factors }}\r\n        </div>\r\n        <div\r\n          class=\"item\"\r\n          v-if=\"label == '资格性评审' || label == '符合性评审'\"\r\n        >\r\n          <div class=\"lable\">修改值：</div>\r\n          <el-switch\r\n            v-model=\"form.modified\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n          >\r\n          </el-switch>\r\n        </div>\r\n        <div\r\n          class=\"item\"\r\n          v-else\r\n        >\r\n          <div class=\"lable\">修改值：</div>\r\n          <el-select\r\n            v-model=\"form.modified\"\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"(score , index) in form.scoreLevel.split(',')\"\r\n              :key=\"index\"\r\n              :label=\"score\"\r\n              :value=\"score\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"item\">\r\n          <div class=\"lable\">修改原因：</div>\r\n          <div style=\"width:80%\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"请输入内容\"\r\n              v-model=\"form.reason\"\r\n            >\r\n            </el-input>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n\r\n      <div\r\n        slot=\"footer\"\r\n        class=\"dialog-footer\"\r\n      >\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"confirm\"\r\n        >确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\r\n//例如：import 《组件名称》 from '《组件路径》';\r\nimport { updateDetail } from \"@/api/evaluation/detail\";\r\n\r\nexport default {\r\n  //import引入的组件需要注入到对象中才能使用\r\n  components: {},\r\n  data() {\r\n    //这里存放数据\r\n    return {\r\n      form: {\r\n        supplier: \"\",\r\n        factors: \"\",\r\n        modified: \"\",\r\n        reason: \"\",\r\n        scoreLevel: \"\",\r\n      },\r\n      dialogFormVisible: false,\r\n      headStyleOne: {\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        \"font-weight\": \"700\",\r\n        \"font-size\": \"18px\",\r\n        background: \"#fff\",\r\n        color: \"#333333\",\r\n      },\r\n      headStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        background: \"#176ADB\",\r\n        color: \"#fff\",\r\n        \"font-size\": \"16px\",\r\n        \"font-weight\": \"700\",\r\n        border: \"0\",\r\n      },\r\n      cellStyle: {\r\n        \"text-align\": \"center\",\r\n        \"font-family\": \"SourceHanSansSC-Bold\",\r\n        height: \"60px\",\r\n        color: \"#000\",\r\n        \"font-size\": \"14px\",\r\n        \"font-weight\": \"700\",\r\n      },\r\n    };\r\n  },\r\n  props: {\r\n    label: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    tableData: {\r\n      type: Array,\r\n      default: [],\r\n    },\r\n    column: {\r\n      type: Array,\r\n      default: [],\r\n    },\r\n  },\r\n\r\n  //监听属性 类似于data概念\r\n  computed: {},\r\n  //监控data中的数据变化\r\n  watch: {},\r\n  //方法集合\r\n  methods: {\r\n    // 编辑\r\n    edit(item, row) {\r\n      this.form.supplier = item;\r\n      this.form.factors = row[\"factor\"];\r\n      this.form.modified = row[item].evaluationResult;\r\n      this.form.reason = \"\";\r\n      this.form.expertEvaluationId = row[item].expertEvaluationId;\r\n      this.form.scoreLevel = row[item].scoreLevel;\r\n      this.dialogFormVisible = true;\r\n    },\r\n    // 确认\r\n    confirm() {\r\n      const data = {\r\n        expertEvaluationId: this.form.expertEvaluationId,\r\n        evaluationResult: this.form.modified,\r\n        evaluationRemark: this.form.reason,\r\n      };\r\n      updateDetail(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.dialogFormVisible = false;\r\n          this.$message.success(\"修改成功\");\r\n          this.$emit(\"update\", \"更新\");\r\n\r\n          // 触发分值修改通知，通知其他页面（如评审汇总页面）\r\n          if (this.$parent && typeof this.$parent.triggerScoreUpdateNotification === 'function') {\r\n            this.$parent.triggerScoreUpdateNotification();\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    tableHeaderClass({ row, column, rowIndex, columnIndex }) {\r\n      return rowIndex == 0 && columnIndex == 0\r\n        ? this.headStyleOne\r\n        : this.headStyle;\r\n    },\r\n  },\r\n  //生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  //生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {},\r\n  beforeCreate() {}, //生命周期 - 创建之前\r\n  beforeMount() {}, //生命周期 - 挂载之前\r\n  beforeUpdate() {}, //生命周期 - 更新之前\r\n  updated() {}, //生命周期 - 更新之后\r\n  beforeDestroy() {}, //生命周期 - 销毁之前\r\n  destroyed() {}, //生命周期 - 销毁完成\r\n  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发\r\n};\r\n</script>\r\n<style>\r\n.custom-header {\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 18px;\r\n  background: #fff;\r\n  color: red;\r\n}\r\n.content {\r\n  width: 80%;\r\n}\r\n.item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n.lable {\r\n  width: 120px;\r\n}\r\n</style>"]}]}