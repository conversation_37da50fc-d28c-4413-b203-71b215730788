package com.ruoyi.eval.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.service.IBusiBidderInfoService;
import com.ruoyi.busi.service.IBusiExtractExpertResultService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.eval.domain.EvalAgainQuote;
import com.ruoyi.eval.domain.EvalExpertEvaluationDetail;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.service.IEvalAgainQuoteService;
import com.ruoyi.eval.service.IEvalExpertEvaluationDetailService;
import com.ruoyi.eval.service.IEvalProjectEvaluationInfoService;
import com.ruoyi.eval.vo.EvaluationResultVo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.eval.mapper.EvalProjectEvaluationProcessMapper;
import com.ruoyi.eval.domain.EvalProjectEvaluationProcess;
import com.ruoyi.eval.service.IEvalProjectEvaluationProcessService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目评审进度Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class EvalProjectEvaluationProcessServiceImpl extends ServiceImpl<EvalProjectEvaluationProcessMapper, EvalProjectEvaluationProcess> implements IEvalProjectEvaluationProcessService {
    /**
     * 查询项目评审进度列表
     *
     * @param evalProjectEvaluationProcess 项目评审进度
     * @return 项目评审进度
     */
    @Autowired
    private IEvalProjectEvaluationInfoService evalProjectEvaluationInfoService;
    @Autowired
    private IEvalProjectEvaluationProcessService evalProjectEvaluationProcessService;
    @Autowired
    private IScoringMethodUitemService iScoringMethodUitemService;
    @Autowired
    private IScoringMethodItemService iScoringMethodItemService;
    @Autowired
    private IEvalExpertEvaluationDetailService evalExpertEvaluationDetailService;
    @Autowired
    private IEvalAgainQuoteService evalAgainQuoteService;
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;
    @Override
    public List<EvalProjectEvaluationProcess> selectList(EvalProjectEvaluationProcess evalProjectEvaluationProcess) {
        QueryWrapper<EvalProjectEvaluationProcess> evalProjectEvaluationProcessQueryWrapper = new QueryWrapper<>();
                        evalProjectEvaluationProcessQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationProcess.getEvaluationProcessId()),"evaluation_process_id",evalProjectEvaluationProcess.getEvaluationProcessId());
                        evalProjectEvaluationProcessQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationProcess.getProjectEvaluationId()),"project_evaluation_id",evalProjectEvaluationProcess.getProjectEvaluationId());
                        evalProjectEvaluationProcessQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationProcess.getScoringMethodItemId()),"scoring_method_item_id",evalProjectEvaluationProcess.getScoringMethodItemId());
                        evalProjectEvaluationProcessQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationProcess.getEvaluationState()),"evaluation_state",evalProjectEvaluationProcess.getEvaluationState());
                        evalProjectEvaluationProcessQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationProcess.getEvaluationResult()),"evaluation_result",evalProjectEvaluationProcess.getEvaluationResult());
                        evalProjectEvaluationProcessQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationProcess.getEvaluationResultRemark()),"evaluation_result_remark",evalProjectEvaluationProcess.getEvaluationResultRemark());
            evalProjectEvaluationProcessQueryWrapper.apply(
                ObjectUtil.isNotEmpty(evalProjectEvaluationProcess.getParams().get("dataScope")),
        evalProjectEvaluationProcess.getParams().get("dataScope")+""
        );
        return list(evalProjectEvaluationProcessQueryWrapper);
    }
    @Transactional
    @Override
    public AjaxResult reEvaluate(Long evaluationProcessId) {
        EvalProjectEvaluationProcess byId = evalProjectEvaluationProcessService.getById(evaluationProcessId);
        byId.setEvaluationState(1);
        boolean b = evalProjectEvaluationProcessService.saveOrUpdate(byId);
        if (b){
            List<ScoringMethodUitem> scoringMethodUitems = iScoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>().eq("scoring_method_item_id", byId.getScoringMethodItemId()));
            List<Long> collect = scoringMethodUitems.stream().map(ScoringMethodUitem::getEntMethodItemId).collect(Collectors.toList());
            List<EvalExpertEvaluationDetail> scoringMethodUitemId = evalExpertEvaluationDetailService.list(new QueryWrapper<EvalExpertEvaluationDetail>().in("scoring_method_uitem_id", collect));
            scoringMethodUitemId.forEach(detail -> detail.setDelFlag(1));
            boolean batchById = evalExpertEvaluationDetailService.updateBatchById(scoringMethodUitemId);
            if (batchById){
                // 添加 WebSocket 消息推送逻辑
                // 获取项目ID和专家列表
                Long projectId = byId.getProjectEvaluationId();
                List<BusiExtractExpertResult> expertResults = busiExtractExpertResultService.list(
                    new QueryWrapper<BusiExtractExpertResult>()
                        .eq("project_id", projectId)
                );

                // 构建详细的消息对象
                JSONObject messageObj = new JSONObject();
                messageObj.put("type", "reEvaluation");
                messageObj.put("projectId", projectId);
                messageObj.put("message", "专家组长发起了重新评审");

                String message = messageObj.toJSONString();

                // 向所有专家推送重新评审消息
                for (BusiExtractExpertResult expertResult : expertResults) {
                    String key = expertResult.getResultId() + "_" + projectId + "_1";
                    WebSocketUsers.sendMessageToUserByKey(key, message);
                }
                
                return AjaxResult.success("删除评审记录成功");
            }else {
                return AjaxResult.error("删除评审记录失败");
            }
        }else {
            return AjaxResult.error("修改评审节点记录失败");

        }
    }

    @Override
    public AjaxResult saveProjectEvaluationProcess(EvalProjectEvaluationProcess evalProjectEvaluationProcess) {
        evalProjectEvaluationProcess.setStartTime(new Date());
        //查询所有的二次报价记录
        List<EvalProjectEvaluationProcess> list = evalProjectEvaluationProcessService.list(new QueryWrapper<EvalProjectEvaluationProcess>()
                .eq("scoring_method_item_id", evalProjectEvaluationProcess.getScoringMethodItemId())
                .eq("project_evaluation_id", evalProjectEvaluationProcess.getProjectEvaluationId())
        );
        //如果是查出来是空的，则是第一次报价，直接保存
        if (list.isEmpty()){
            boolean b = evalProjectEvaluationProcessService.saveOrUpdate(evalProjectEvaluationProcess);
            if (b){
                return AjaxResult.success("保存成功");
            }else {
                return AjaxResult.error("保存失败");

            }
        }else {
            //如果不是第一次报价
            //查询ScoringMethodItem判断如果是投标报价打分
            ScoringMethodItem byId = iScoringMethodItemService.getById(evalProjectEvaluationProcess.getScoringMethodItemId());
            if (byId.getItemCode().equals("tbbjdf")){
                //如果minutes为空，表示第一次发起二次报价，直接保存minutes报价倒计时并返回。
                if(null==list.get(0).getMinutes()){
                    list.get(0).setStartTime(evalProjectEvaluationProcess.getStartTime());
//                            list.get(0).setNum(list.get(0).getNum()+1);
                    list.get(0).setMinutes(evalProjectEvaluationProcess.getMinutes());
                    evalProjectEvaluationProcessService.updateById(list.get(0));
                    return AjaxResult.success("更新成功",list.get(0));
                }
                //查询出项目评审信息
                EvalProjectEvaluationInfo evalProjectEvaluationInfo = evalProjectEvaluationInfoService.getById(evalProjectEvaluationProcess.getProjectEvaluationId());
                //投标报价次数list.size()
                //for (EvalProjectEvaluationProcess projectEvaluationProcess : list) {
                    //查询当前有效供应商
                    List<BusiBidderInfo> bidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>()
                            .eq("project_id", evalProjectEvaluationInfo.getProjectId())
                            .eq("is_abandoned_bid", 0)
                            .eq("del_flag", 0)
                    );
                    //有效供应商的id集合
                    List<Long> bidderIds = bidderInfos.stream().map(BusiBidderInfo::getBidderId).collect(Collectors.toList());

                    //查询报价记录
                    List<EvalAgainQuote> evalAgainQuotes = evalAgainQuoteService.list(new QueryWrapper<EvalAgainQuote>()
                            .eq("project_evaluation_id", evalProjectEvaluationProcess.getProjectEvaluationId())
                            .eq("quote_number", list.get(0).getNum())
                    );
                    // 提取报价记录中的供应商id集合
                    List<Long> quotedBidderIds = evalAgainQuotes.stream().map(EvalAgainQuote::getEntId).collect(Collectors.toList());
                    //取出当前所报价次数的二次报价记录
//                    List<EvalProjectEvaluationProcess> evaluationProcessList = list.stream()
//                            .filter(process -> process.getNum() == maxNum)
//                            .collect(Collectors.toList());

                    //如果包含则返回 true表示所有供应商都已经报价则保存新增一次二次报价   否则返回 false
                    if (quotedBidderIds.containsAll(bidderIds)){
                        list.get(0).setStartTime(evalProjectEvaluationProcess.getStartTime());
                        list.get(0).setMinutes(evalProjectEvaluationProcess.getMinutes());
                        list.get(0).setNum(list.get(0).getNum()+1);
                        evalProjectEvaluationProcessService.updateById(list.get(0));
                        return AjaxResult.success("更新成功",list.get(0));
                    }else {
                        //没有全部报价，要判断时间是否过期
                        Date startTimeDate = list.get(0).getStartTime();
                        // 将 Date 转换为 LocalDateTime
                        LocalDateTime startTime = startTimeDate.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();
                        // 计算开始时间加上minutes分钟
                        //LocalDateTime startTimePlusMinutes = startTime.plusMinutes(list.get(0).getMinutes());

                        // 获取当前时间
                        LocalDateTime now = LocalDateTime.now();
                        // 计算当前时间与startTime的时间差（以分钟为单位）
                        long diffInMinutes = Duration.between(startTime, now).toMinutes();
                        // 判断时间差对应的分钟数是否小于list.get(0).getMinutes()
                        boolean isLessThan = diffInMinutes < list.get(0).getMinutes();
                        if (isLessThan){
                            return AjaxResult.error("第"+list.get(0).getNum()+"次报价未结束");
                        }else {
                            list.get(0).setStartTime(evalProjectEvaluationProcess.getStartTime());
                            list.get(0).setNum(list.get(0).getNum()+1);
                            list.get(0).setMinutes(evalProjectEvaluationProcess.getMinutes());
                            evalProjectEvaluationProcessService.updateById(list.get(0));
                            return AjaxResult.success("更新成功",list.get(0));
                        }
                    }
            }
            return AjaxResult.error("item记录已存在");
        }
    }

    @Override
    public AjaxResult updateEvalProjectEvaluationProcess(EvalProjectEvaluationProcess evalProjectEvaluationProcess) {
        updateById(evalProjectEvaluationProcess);
        List<EvaluationResultVo> evaluationResultVoList = JSON.parseObject(evalProjectEvaluationProcess.getEvaluationResult(),new TypeReference<List<EvaluationResultVo>>() {});
        System.out.println("evaluationResultVoList:"+JSONObject.toJSONString(evaluationResultVoList));
        EvalProjectEvaluationProcess process = getById(evalProjectEvaluationProcess.getEvaluationProcessId());
        if(process != null){
            ScoringMethodItem item = iScoringMethodItemService.getById(process.getScoringMethodItemId());
            if(item.getItemMode()!=null && item.getItemMode()==2){
                EvalProjectEvaluationInfo evalProjectEvaluationInfo = evalProjectEvaluationInfoService.getById(process.getProjectEvaluationId());
                for (EvaluationResultVo evaluationResultVo : evaluationResultVoList) {
                    System.out.println("------------ evaluationResultVo  "+evaluationResultVo.getBidder() + " -------------------- ");
                    System.out.println(evaluationResultVo);
                    System.out.println(evaluationResultVo.getResult());
                    System.out.println(Boolean.parseBoolean(evaluationResultVo.getResult()));
                    if (!Boolean.parseBoolean(evaluationResultVo.getResult())){
                        BusiBidderInfo one = busiBidderInfoService.getOne(new QueryWrapper<BusiBidderInfo>()
                                .eq("project_id", evalProjectEvaluationInfo.getProjectId())
                                .eq("bidder_id", evaluationResultVo.getBidder())
                        );
                        one.setIsAbandonedBid(1);
                        busiBidderInfoService.saveOrUpdate(one);
                    }
                }
            }
            return AjaxResult.success();
        }
        return AjaxResult.error("评审节点获取错误");
    }

    public static void main(String[] args) {
        EvaluationResultVo e = new EvaluationResultVo();
        e.setResult("true");
        e.setBidder(1111);
        e.setGys("dfd");
        System.out.println(e);
        System.out.println(e.getResult());
        System.out.println(Boolean.parseBoolean(e.getResult()));
    }
}
